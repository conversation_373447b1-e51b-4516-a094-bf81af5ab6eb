<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>微信群扫码</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/static/layui/css/layui.css" media="all">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            background-color: transparent;
        }
        
        .modal-container {
            position: relative;
            width: 460px;
            margin: 0 auto;
            background-color: #6a5acd;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        }
        
        .alert-content {
            background-color: #fff;
            margin: 15px;
            border-radius: 8px;
            padding: 20px;
        }
        
        .header {
            background-color: #ff4d4f;
            padding: 15px 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
            color: #fff;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .header-text {
            font-size: 16px;
            margin: 0;
            line-height: 1.5;
        }
        
        .message-box {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .message-text {
            font-size: 14px;
            color: #333;
            line-height: 1.8;
            margin: 0;
        }
        
        .notice-box {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 0 10px;
        }
        
        .clock-icon {
            color: #ff4d4f;
            font-size: 20px;
            margin-right: 10px;
        }
        
        .notice-text {
            font-size: 12px;
            color: #ff4d4f;
        }
        
        .button-group {
            display: flex;
            justify-content: space-between;
        }
        
        .btn {
            padding: 10px 0;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            flex: 1;
            text-align: center;
        }
        
        .btn-cancel {
            background-color: #f0f0f0;
            color: #333;
            margin-right: 10px;
        }
        
        .btn-confirm {
            background-color: #ff4d4f;
            color: white;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="modal-container">
        <div class="alert-content">
            <div class="header">
                <div class="header-text">警告！危险操作</div>
                <div class="header-text" style="font-size: 14px;">请考虑后再执行</div>
            </div>
            
            <div class="message-box">
                <p class="message-text">
                    已打开微信扫描工具，准备开始执行强制扫描客户微信内<br>
                    所有群二维码，预计20分钟之内完成。
                </p>
            </div>
            
            <div class="notice-box">
                <i class="layui-icon layui-icon-time clock-icon"></i>
                <div class="notice-text">
                    注意：开始执行完成需时，请在半个小时内完成对接操作。
                </div>
            </div>
            
            <div class="button-group">
                <button class="btn btn-cancel" id="cancelBtn">取消</button>
                <button class="btn btn-confirm" id="confirmBtn">确认执行</button>
            </div>
        </div>
    </div>

    <script src="/static/jquery/jquery.min.js"></script>
    <script src="/static/layui/layui.js"></script>
    <script>
        layui.use(['layer'], function() {
            var layer = layui.layer;
            var $ = layui.jquery;
            
            // 取消按钮点击事件
            $('#cancelBtn').click(function() {
                parent.layer.closeAll();
            });
            
            // 确认执行按钮点击事件
            $('#confirmBtn').click(function() {
                layer.msg('开始执行微信群扫码操作...', {icon: 16, time: 2000});
                
                // 模拟操作，2秒后关闭
                setTimeout(function() {
                    parent.layer.closeAll();
                }, 2000);
            });
        });
    </script>
</body>
</html> 