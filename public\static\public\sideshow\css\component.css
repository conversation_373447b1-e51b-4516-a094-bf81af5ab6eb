/* Header */
.large-header {
	position: relative;
	width: 100%;
	background: #333;
	overflow: hidden;
	background-size: cover;
	background-position: center center;
	z-index: 1;
}

.demo-1 .large-header {
	background-image: url('../img/yuzhou.jpg');
}

.demo-2 .large-header {
	background-image: url('../img/night.gif');
	background-size: auto;
	background-position:auto;
	background-repeat:repeat;
}


.demo-3 .large-header {
	background:#fff;
}

.demo-4 .large-header {
	background-image: url('../img/bluesky.jpg');
	background-position: center bottom;
}


.main-title {
	position: absolute;
	margin: 0;
	padding: 0;
	color: #f9f1e9;
	text-align: center;
	top: 50%;
	left: 50%;
	-webkit-transform: translate3d(-50%,-50%,0);
	transform: translate3d(-50%,-50%,0);
}

.demo-1 .main-title, 
.demo-3 .main-title {
	text-transform: uppercase;
	font-size: 4.2em;
	letter-spacing: 0.1em;
}

.demo-2 .main-title {
	font-family: 'Clicker Script', cursive;
	font-weight: normal;
	font-size: 8em;
	padding-left: 10px;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
}

.demo-2 .main-title::before {
	content: '';
	width: 20vw;
	height: 20vw;
	min-width: 3.5em;
	min-height: 3.5em;
	background: url(../img/deco.svg) no-repeat center center;
	background-size: cover;
	position: absolute;
	top: 50%;
	left: 50%;
	border-radius: 50%;
	z-index: -1;
	-webkit-transform: translate3d(-50%,-50%,0);
	transform: translate3d(-50%,-50%,0);
}

.demo-3 .main-title {
	padding: 10px 40px;
	border: 10px double #f9f1e9;
	text-transform: uppercase;
	font-family: Londrina Outline, sans-serif;
}

.demo-4 .main-title {
	font-size: 6em;
	font-weight: 300;
	padding: 10px 30px;
	text-transform: uppercase;
	color: #222;
}

.main-title .thin {
	font-weight: 200;
}

@media only screen and (max-width : 768px) {
	.demo-1 .main-title, 
	.demo-3 .main-title,
	.demo-4 .main-title {
		font-size: 3em;
	}

	.demo-2 .main-title {
		font-size: 4em;
	}
}