{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.2.5", "topthink/framework": "^6.1.0", "topthink/think-orm": "^2.0", "topthink/think-filesystem": "^1.0", "topthink/think-multi-app": "^1.1", "topthink/think-view": "^1.0", "topthink/think-captcha": "^3.0", "phpoffice/phpspreadsheet": "^1.19"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}