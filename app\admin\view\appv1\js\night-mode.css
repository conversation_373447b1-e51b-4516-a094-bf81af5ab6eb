/* 夜间模式开关样式 */
.night-mode-toggle {
    display: flex;
    align-items: center;
    margin-right: 15px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 5px 10px;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.mode-label {
    font-size: 12px;
    margin: 0 5px;
    color: #666;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin: 0 5px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

input:checked + .slider {
    background: linear-gradient(135deg, #6f42c1, #8c68c9);
}

input:focus + .slider {
    box-shadow: 0 0 1px #6f42c1;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

/* 夜间模式主题 */
body.night-mode {
    background-color: #1a1a2e;
    color: #e6e6e6;
}

body.night-mode .layui-body {
    background-color: #1a1a2e;
}

body.night-mode .main-content {
    background-color: #1f1f3a;
    color: #e6e6e6;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

body.night-mode .top-controls-bar {
    background-color: #272741;
    border-color: #333355;
}

body.night-mode .page-select {
    background-color: #2c2c4a;
    color: #e6e6e6;
    border-color: #3e3e5e;
}

body.night-mode .notice-tag {
    background-color: #272741;
    color: #e6e6e6;
}

body.night-mode .search-container input {
    background-color: #2c2c4a;
    color: #e6e6e6;
    border-color: #3e3e5e;
}

body.night-mode .user-info-container {
    background-color: #272741;
    border-color: #333355;
}

body.night-mode .user-name {
    color: #e6e6e6;
}

body.night-mode .layui-table {
    background-color: #1f1f3a;
    color: #e6e6e6;
}

body.night-mode .layui-table td {
    border-color: #333355;
}

body.night-mode .layui-table tbody tr:hover {
    background-color: #292952;
}

body.night-mode .layui-footer {
    background-color: #1f1f3a;
    color: #999;
    border-top: 1px solid #333355;
}

body.night-mode .header-panel {
    background: linear-gradient(135deg, #483285, #5c4199);
}

body.night-mode .relative-item-modal,
body.night-mode .app-item-modal {
    background-color: #272741;
    color: #e6e6e6;
}

body.night-mode .modal-content,
body.night-mode .remark-modal-content {
    background-color: #1f1f3a;
    color: #e6e6e6;
}

body.night-mode .modal-body,
body.night-mode .remark-modal-body {
    background-color: #1f1f3a;
    color: #e6e6e6;
}

body.night-mode .remark-textarea {
    background-color: #2c2c4a;
    color: #e6e6e6;
    border-color: #3e3e5e;
}

body.night-mode .edit-remark {
    background-color: #272741;
    color: #e6e6e6;
}

body.night-mode .layui-layer-content {
    background-color: #1f1f3a;
    color: #e6e6e6;
}

body.night-mode .pagination > li > a,
body.night-mode .pagination > li > span {
    background-color: #272741;
    color: #e6e6e6;
}

body.night-mode .pagination-info {
    background-color: #272741;
    color: #e6e6e6;
}

body.night-mode .stat-card {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
}

body.night-mode .mode-label {
    color: #e6e6e6;
}

body.night-mode .batch-actions {
    background-color: #1f1f3a;
    border-color: #333355;
}

body.night-mode .layui-side-menu {
    background-color: #151521;
}

body.night-mode .layui-nav-tree .layui-nav-item a {
    background-color: rgba(40, 40, 60, 0.5);
}

body.night-mode .layui-nav-tree .layui-nav-item a:hover {
    background-color: #252540;
}

body.night-mode .function-btn.blue, 
body.night-mode .social-cmd-btn, 
body.night-mode .app-function-new {
    opacity: 0.9;
} 