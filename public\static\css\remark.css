/* 编辑备注按钮样式 */
.edit-remark {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: #f9f9f9;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 10px;
}

.edit-remark:hover {
    background-color: #f0f0f0;
    transform: translateY(-2px);
}

.edit-remark i {
    color: #8257e6;
    font-size: 14px;
}

.edit-remark span {
    color: #666;
    font-size: 13px;
}

/* 备注弹窗样式 */
#remarkModal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease-in-out;
}

.remark-modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 0;
    width: 500px;
    max-width: 90%;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    transition: all 0.3s ease-in-out;
    transform: scale(0.9);
    opacity: 0;
}

#remarkModal.show .remark-modal-content {
    transform: scale(1);
    opacity: 1;
}

.remark-modal-header {
    background: linear-gradient(to right, #1e9fff, #39c7ff);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px 8px 0 0;
}

.remark-modal-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
}

.remark-modal-close {
    color: white;
    font-size: 24px;
    cursor: pointer;
}

.remark-modal-body {
    padding: 20px;
}

.remark-textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    resize: vertical;
    font-size: 14px;
    margin-bottom: 15px;
    transition: all 0.2s;
    border: 1px solid #ddd;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.remark-textarea:focus {
    border-color: #1e9fff;
    box-shadow: 0 0 5px rgba(30,159,255,0.5);
}

.remark-save-btn {
    background: linear-gradient(to right, #1e9fff, #39c7ff);
    border: none;
    color: white;
    border-radius: 4px;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.2s;
    margin-top: 15px;
    font-weight: bold;
}

.remark-save-btn:hover {
    background: linear-gradient(to right, #0e8fee, #25b6f5);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.remark-save-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.remark-save-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
} 