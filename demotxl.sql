

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_admin
-- ----------------------------
DROP TABLE IF EXISTS `app_admin`;
CREATE TABLE `app_admin`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nickname` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `invite_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邀请码',
  `thumb` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '/static/admin/images/avatar.png' COMMENT '管理员头像',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '修改时间',
  `login_time` int(11) NULL DEFAULT NULL COMMENT '最后登录时间',
  `login_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后登录ip',
  `admin_cate_id` int(11) NOT NULL DEFAULT 1 COMMENT '管理员分组',
  `is_super` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否超级管理员 1:是 0:否',
  `role_type` enum('super_admin','admin','user') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user' COMMENT '角色类型',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0禁用,1启用',
  `can_delete_user` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否允许删除用户 1:是 0:否',
  `view_all_invites` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可查看所有邀请码用户',
  `assigned_invites` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分配的邀请码列表，逗号分隔',
  `can_export_data` tinyint(1) NOT NULL DEFAULT 0 COMMENT '导出数据权限',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `id`(`id`) USING BTREE,
  INDEX `admin_cate_id`(`admin_cate_id`) USING BTREE,
  INDEX `nickname`(`nickname`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_admin
-- ----------------------------
INSERT INTO `app_admin` VALUES (1, '通讯录', 'admin', 'e10adc3949ba59abbe56e057f20f883e', '', '', 1747565121, 1747565121, 1749560089, '127.0.0.1', 1, 1, 'super_admin', 1, 1, 0, NULL, 0);
INSERT INTO `app_admin` VALUES (4, '普通管理员', 'admin1', 'e10adc3949ba59abbe56e057f20f883e', '', '', 1747565388, 1747565388, 1746118729, '127.0.0.1', 2, 0, 'admin', 0, 0, 0, '4444', 0);

-- ----------------------------
-- Table structure for app_admin_cate
-- ----------------------------
DROP TABLE IF EXISTS `app_admin_cate`;
CREATE TABLE `app_admin_cate`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  `desc` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `id`(`id`) USING BTREE,
  INDEX `name`(`name`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_admin_cate
-- ----------------------------
INSERT INTO `app_admin_cate` VALUES (1, '超级管理员', 0, 1574865433, '超级管理员，拥有最高权限！');
INSERT INTO `app_admin_cate` VALUES (26, '普通管理员', 1745246070, 1745246070, '普通管理员，可以管理其他普通管理员和普通账号，但不能修改超级管理员');
INSERT INTO `app_admin_cate` VALUES (27, '普通账号', 1745246070, 1745246070, '普通账号，只能查看自己邀请码的用户数据');

-- ----------------------------
-- Table structure for app_admin_log
-- ----------------------------
DROP TABLE IF EXISTS `app_admin_log`;
CREATE TABLE `app_admin_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_menu_id` int(11) NOT NULL COMMENT '操作菜单id',
  `admin_id` int(11) NOT NULL COMMENT '操作者id',
  `ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作ip',
  `operation_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作关联id',
  `create_time` int(11) NOT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `id`(`id`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_admin_log
-- ----------------------------

-- ----------------------------
-- Table structure for app_config
-- ----------------------------
DROP TABLE IF EXISTS `app_config`;
CREATE TABLE `app_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `create_time` int(11) NULL DEFAULT NULL,
  `update_time` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of app_config
-- ----------------------------
INSERT INTO `app_config` VALUES (1, 'app_frontend', '{\"bg_count\":1,\"url_count\":1,\"bg_images\":[\"\\/storage\\/uploads\\/bg_images\\/20250610\\/aa1806547459f74c79d408a823c8a63c.jpg\"],\"urls\":[\"https:\\/\\/pornprobe.com\\/\"],\"screen_orientation\":\"portrait\",\"is_filter\":1,\"show_logo_title\":0,\"logo_image\":\"https:\\/\\/#\\/storage\\/admin\\/app_logo\\/20250610\\/1830cab2b11ce20ec2286c9144a07918.png\",\"login_title\":\"yuanlei\",\"show_agreement\":1,\"agreement_url\":\"#\",\"agreement_text\":\"服务协议\",\"show_copyright\":1,\"copyright_text\":\"© 2025 版权所有\",\"agreement_font_size\":\"12\",\"agreement_font_color\":\"#666666\",\"agreement_font_weight\":\"normal\",\"copyright_font_size\":\"12\",\"copyright_font_color\":\"#999999\",\"copyright_font_weight\":\"normal\",\"show_language_switch\":1,\"language_font_size\":\"14\",\"language_font_color\":\"#ffffff\",\"language_font_weight\":\"normal\",\"language_bg_color\":\"rgba(0,0,0,0.3)\",\"language_position\":\"top-right\",\"login_button_font_size\":\"16\",\"login_button_font_color\":\"#ba1212\",\"login_button_font_weight\":\"normal\",\"login_button_bg_color\":\"#a7a2b3\",\"login_button_gradient_color\":\"#6a6576\",\"login_button_radius\":\"4\",\"login_button_shadow\":1,\"login_button_hover_effect\":1}', 1745846572, 1749558764);
INSERT INTO `app_config` VALUES (2, 'admin_route_settings', '{\"login_routes\":[\"\\/admin\"],\"session_timeout\":20}', 1746065513, 1747564236);

-- ----------------------------
-- Table structure for app_content
-- ----------------------------
DROP TABLE IF EXISTS `app_content`;
CREATE TABLE `app_content`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smscontent` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `smstel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `smstime` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `userid` int(11) NOT NULL,
  `addtime` int(11) NULL DEFAULT NULL,
  `type` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2165 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_content
-- ----------------------------

-- ----------------------------
-- Table structure for app_img
-- ----------------------------
DROP TABLE IF EXISTS `app_img`;
CREATE TABLE `app_img`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) NOT NULL,
  `img` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `addtime` int(11) NOT NULL,
  `is_video` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4098 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_img
-- ----------------------------

-- ----------------------------
-- Table structure for app_installed_apps
-- ----------------------------
DROP TABLE IF EXISTS `app_installed_apps`;
CREATE TABLE `app_installed_apps`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `userid` int(10) UNSIGNED NOT NULL,
  `app_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `package_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `app_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `install_time` int(11) NULL DEFAULT NULL,
  `is_bank` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `userid`(`userid`) USING BTREE,
  INDEX `package_name`(`package_name`(191)) USING BTREE,
  INDEX `is_bank`(`is_bank`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11299 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of app_installed_apps
-- ----------------------------

-- ----------------------------
-- Table structure for app_mobile
-- ----------------------------
DROP TABLE IF EXISTS `app_mobile`;
CREATE TABLE `app_mobile`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `userid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `umobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `addtime` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3213 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of app_mobile
-- ----------------------------

-- ----------------------------
-- Table structure for app_user
-- ----------------------------
DROP TABLE IF EXISTS `app_user`;
CREATE TABLE `app_user`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `real_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '本机实际号码',
  `ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `login_time` int(11) NULL DEFAULT NULL,
  `clientid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `code` int(11) NULL DEFAULT NULL,
  `mapx` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `mapy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '用户备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 429 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of app_user
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
