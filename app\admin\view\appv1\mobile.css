/* 基本响应式断点 */
@media screen and (max-width: 768px) {
  /* 移动端菜单交互样式 */
  .layui-layout-admin .layui-side {
    position: fixed;
    width: 240px !important;
    transform: translateX(-100%);
    z-index: 1001;
    transition: transform 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  }

  /* 打开状态的菜单 */
  .layui-layout-admin .layui-side.menu-open {
    transform: translateX(0);
  }

  /* 菜单遮罩层 */
  .menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
  }
  
  .menu-overlay.active {
    display: block;
  }

  /* 内容区域全宽 */
  .layui-layout-admin .layui-body {
    left: 0 !important;
    width: 100% !important;
    transition: left 0.3s ease;
  }

  /* 底部版权区域全宽 */
  .layui-layout-admin .layui-footer {
    left: 0 !important;
    width: 100% !important;
  }
  
  /* 菜单切换按钮 */
  .menu-toggle-btn {
    position: fixed;
    top: 10px;
    left: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6f42c1, #8c68c9);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    z-index: 1002;
    border: none;
  }
  
  .menu-toggle-btn i {
    font-size: 18px;
    transition: transform 0.3s ease;
  }
  
  /* 按钮图标旋转效果 */
  .menu-toggle-btn.active i {
    transform: rotate(180deg);
  }
  
  /* 给主内容区域添加内边距，防止内容被菜单按钮遮挡 */
  .main-content {
    padding-top: 50px !important;
  }
  
  /* 表格横向滚动容器 */
  .table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* 提升iOS滚动体验 */
  }
  
  /* 确保表格在容器内可以完整显示 */
  .table-responsive .layui-table {
    min-width: 1100px; /* 增加表格最小宽度，确保每列有足够空间 */
    width: 100%;
  }
  
  /* 表格文字和内容的优化 */
  .layui-table th, 
  .layui-table td,
  .info-item,
  .info-value,
  .relative-name,
  .relative-phone,
  .app-item span,
  .function-btn,
  .social-cmd-btn,
  .app-function-new,
  .action-btn {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  /* 增加按钮的宽度，避免文字被截断 */
  .function-btn, 
  .social-cmd-btn, 
  .app-function-new, 
  .action-btn {
    width: 100%;
    padding: 8px 5px;
    min-width: 90px;
  }
  
  /* 优化信息列表布局 */
  .info-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  /* 相关项目列表布局优化 */
  .relative-list, 
  .app-list {
    max-width: 100%;
    overflow: hidden;
  }
  
  /* 优化头部面板在移动端的显示 */
  .header-panel {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-version {
    margin-top: 10px;
    width: 100%;
    justify-content: flex-start;
  }
  
  /* 顶部控制条优化 */
  .top-controls-bar {
    flex-direction: column;
    gap: 10px;
  }
  
  .page-selector {
    width: 100%;
    justify-content: space-between;
  }
  
  .notice-tags {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 5px;
    white-space: nowrap;
  }
  
  .stats-badges {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .search-container {
    width: 100%;
  }
  
  .user-info-container {
    width: 100%;
  }
  
  /* 统计卡片优化 */
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .stat-card {
    min-height: 80px;
    padding: 10px;
  }
  
  .stat-card .stat-icon {
    font-size: 40px;
  }
  
  .stat-card .stat-title {
    font-size: 14px;
    margin-bottom: 10px;
  }
  
  .stat-card .stat-value {
    font-size: 18px;
  }
  
  /* 批量操作区域优化 */
  .batch-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .action-left, .action-right {
    width: 100%;
  }
  
  /* 分页样式优化 */
  .pagination-container {
    overflow-x: auto;
  }
  
  .pagination {
    white-space: nowrap;
  }
  
  /* 弹窗样式适配 */
  .layui-layer {
    width: 95% !important;
    left: 2.5% !important;
    margin-left: 0 !important;
  }
  
  /* 修改备注弹窗适配 */
  .remark-modal-content {
    width: 95%;
    max-width: 95%;
  }
  
  /* 优化语言切换器 */
  .language-switcher {
    width: 100%;
  }
  
  .language-dropdown {
    width: 100%;
  }
  
  .language-current {
    width: 100%;
    justify-content: center;
  }
  
  /* 提示条优化 */
  .status-toast {
    max-width: 90%;
    left: 5%;
    right: 5%;
  }
  
  .notification-toast {
    max-width: 90%;
  }
}

/* 小型手机屏幕的额外优化 */
@media screen and (max-width: 480px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .app-function-new, .social-cmd-btn, .function-btn, .action-btn {
    font-size: 11px;
    padding: 5px 0;
  }
  
  .app-function-new i, .social-cmd-btn i, .function-btn i, .action-btn i {
    font-size: 13px;
  }
  
  /* 操作按钮文字隐藏，只保留图标 */
  .batch-btn span {
    display: none;
  }
  
  .batch-btn i {
    margin-right: 0;
  }
  
  /* 更紧凑的表格内容 */
  .layui-table tbody tr td {
    padding: 10px 5px !important;
  }
  
  .info-list {
    gap: 5px;
  }
  
  .info-item {
    font-size: 11px;
  }
  
  .info-value {
    font-size: 11px;
  }
} 