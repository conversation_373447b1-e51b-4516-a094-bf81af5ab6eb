<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登录日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/static/font-awesome/css/font-awesome.min.css" media="all" />
    <style type="text/css">
        body {
            background-color: #f5f7fa;
            padding: 15px;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .log-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        .header {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #38bdf8;
            color: #fff;
        }
        .header i {
            font-size: 18px;
            margin-right: 10px;
        }
        .header-title {
            font-size: 16px;
            font-weight: 500;
        }
        .user-info {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background-color: #f8fafc;
        }
        .info-item {
            display: flex;
            margin-bottom: 8px;
        }
        .info-item:last-child {
            margin-bottom: 0;
        }
        .info-label {
            width: 100px;
            color: #64748b;
            font-size: 14px;
        }
        .info-value {
            color: #334155;
            font-weight: 500;
            font-size: 14px;
        }
        .log-table {
            width: 100%;
            border-collapse: collapse;
        }
        .log-table th {
            background-color: #f1f5f9;
            color: #334155;
            font-weight: 600;
            text-align: left;
            padding: 12px 15px;
            font-size: 14px;
        }
        .log-table td {
            padding: 12px 15px;
            border-top: 1px solid #e2e8f0;
            font-size: 14px;
            color: #475569;
        }
        .log-table tr:hover {
            background-color: #f8fafc;
        }
        .empty-message {
            padding: 30px;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }
        /* 移动端响应式适配 */
        @media screen and (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .log-container {
                box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
            }
            
            .header {
                padding: 12px;
            }
            
            .header-title {
                font-size: 15px;
            }
            
            .user-info {
                padding: 12px;
            }
            
            .info-item {
                margin-bottom: 6px;
            }
            
            .info-label {
                width: 80px;
                font-size: 13px;
            }
            
            .info-value {
                font-size: 13px;
            }
            
            /* 表格滚动适配 */
            .log-table {
                display: block;
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }
            
            .log-table th,
            .log-table td {
                white-space: nowrap;
                padding: 10px 12px;
                font-size: 13px;
            }
            
            .empty-message {
                padding: 20px;
                font-size: 13px;
            }
        }
        /* 针对更小屏幕的优化 */
        @media screen and (max-width: 480px) {
            .log-container {
                border-radius: 6px;
            }
            
            .header {
                padding: 10px;
            }
            
            .header i {
                font-size: 16px;
            }
            
            .header-title {
                font-size: 14px;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
                margin-bottom: 8px;
            }
            
            .info-label {
                width: 100%;
                margin-bottom: 3px;
            }
            
            .info-value {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="log-container">
        <div class="header">
            <i class="fa fa-history"></i>
            <div class="header-title">登录日志 - {$admin.nickname}</div>
        </div>
        
        <div class="user-info">
            <div class="info-item">
                <div class="info-label">用户名：</div>
                <div class="info-value">{$admin.name}</div>
            </div>
            <div class="info-item">
                <div class="info-label">最后登录：</div>
                <div class="info-value">{$lastLogin.time}</div>
            </div>
            <div class="info-item">
                <div class="info-label">登录IP：</div>
                <div class="info-value">{$lastLogin.ip}</div>
            </div>
        </div>
        
        {empty name="logs"}
            <div class="empty-message">
                <i class="fa fa-info-circle"></i> 暂无登录记录
            </div>
        {else}
            <table class="log-table">
                <thead>
                    <tr>
                        <th>登录时间</th>
                        <th>登录IP</th>
                    </tr>
                </thead>
                <tbody>
                    {foreach $logs as $log}
                    <tr>
                        <td>{$log.login_time}</td>
                        <td>{$log.ip}</td>
                    </tr>
                    {/foreach}
                </tbody>
            </table>
        {/empty}
    </div>
</body>
</html> 