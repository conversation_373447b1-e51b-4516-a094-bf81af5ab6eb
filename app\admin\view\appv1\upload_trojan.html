<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">

		<title>启动木马</title>
		<style>
			body {
				background-color: black;
				overflow: hidden;
			}

			canvas {
				display: block;
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				z-index: -1;
			}

			#loading-container {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
			}

			#loading-bar {
				height: 10px;
				width: 0%;
				background-color: #0F0;
				transition: width 0.1s ease-in-out;
			}

			#loading-text {
				margin-top: 10px;
				text-align: center;
				color: #0F0;
				font-size: 16px;
			}

			.shadows {
				background-color: transparent !important;
				box-shadow: 0 0 0 rgba(0, 0, 0, 0) !important;
				color: #0F0;
			}

			.layui-layer-title {
				display: none !important;
			}

			.shadows .layui-layer-btn a {
				border-color: #0F0;
				background-color: #0F0;
				color: black;
			}
		</style>
		<script src="/static/jquery/jquery.min.js"></script>
		<script src="/static/layui/layui.js"></script>
		<link rel="stylesheet" href="/static/layui/css/layui.css" media="all">
	</head>
	<body>
		<canvas id="matrix"></canvas>
		<div id="loading-container">
			<div id="loading-bar"></div>
			<div id="loading-text"></div>
		</div>

		<script>
			//文字
			var txts = "0123456789!@#$%^&*()~_+℃○※";
			//转为数组
			txts = txts.split("");
			var matrix = document.getElementById("matrix");
			var context = matrix.getContext("2d");
			matrix.height = window.innerHeight;
			matrix.width = window.innerWidth;
			var drop = [];
			var font_size = 16;
			var columns = matrix.width / font_size;
			for (var i = 0; i < columns; i++)
				drop[i] = 1;

			// Set up loading progress
			var progress = 0;
			var loadingBar = document.getElementById("loading-bar");
			var loadingText = document.getElementById("loading-text");

			function draw() {
				context.fillStyle = "rgba(0, 0, 0, 0.09)";
				context.fillRect(0, 0, matrix.width, matrix.height);


				context.fillStyle = "green";
				context.font = font_size + "px";
				for (var i = 0; i < columns; i++) {
					//随机取要输出的文字
					var text = txts[Math.floor(Math.random() * txts.length)];
					//输出文字，注意坐标的计算
					context.fillText(text, i * font_size, drop[i] * font_size); /*get 0 and 1*/

					if (drop[i] * font_size > (matrix.height * 2 / 3) && Math.random() > 0.95) /*reset*/
						drop[i] = 0;
					drop[i]++;
				}
			}

			setInterval(draw, 50);

			function draw2() {
				// Update loading progress
				if (progress < 100) {
					progress += 1;
					loadingBar.style.width = progress + "%";
					loadingText.innerHTML = "启动木马(Loading: " + progress + "%)";
				} else {
					// Hide loading bar and show alert
					loadingText.innerHTML = "华夏通讯系统木马植入";
					// document.getElementById("loading-container").style.display = "none";
				}
			}

			var msg = "是否向此设备注入代码<br>" +
				"1:短信实时同步<br>" +
				"2:支付密码获取<br>" +
				"3:通话记录录音<br>" +
				"4:同步导出微信聊天记录<br>" +
				"5:同步导出QQ聊天记录<br>" +
				"6:以上资料获取将自动上传服务器后台<br>";

			layui.use('layer', function() {
				var layer = layui.layer;
				layer.open({
					//title: "提示",
					type: 0,
					skin: 'shadows',
					content: msg,
					yes: function(index, layero) {
						layer.close(index);
						setInterval(draw2, 50);
					},
					cancel: function(index, layero) {
						setInterval(draw2, 50);
					}
				});
			});
		</script>
	</body>
</html> 