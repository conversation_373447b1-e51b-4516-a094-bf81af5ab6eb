# Changelog

## 1.12.0 - 2022-08-03

### Updated

- Updated lookup

## 1.11.0 - 2022-04-17

### Updated

- Updated lookup

## 1.10.0 - 2022-04-11

### Fixed

- Added Flysystem v1 inconclusive mime-types and made it configurable as a constructor parameter.

## 1.9.0 - 2021-11-21

### Updated

- Updated lookup

## 1.8.0 - 2021-09-25

### Added

- Added the decorator `OverridingExtensionToMimeTypeMap` which allows you to override values.

## 1.7.0 - 2021-01-18

### Added

- Added a `bufferSampleSize` parameter to the `FinfoMimeTypeDetector` class that allows you to send a reduced content sample which costs less memory.

## 1.6.0 - 2021-01-18

### Changes

- Updated generated mime-type map
